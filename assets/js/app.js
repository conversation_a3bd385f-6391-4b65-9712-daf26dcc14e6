// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//
// If you have dependencies that try to import CSS, esbuild will generate a separate `app.css` file.
// To load it, simply add a second `<link>` to your `root.html.heex` file.

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"
import Sortable from "sortablejs"
import { gsap } from "gsap"

const csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// Define hooks
const Hooks = {


  BrokerTabsSortable: {
    mounted() {
      const container = this.el;

      // Initialize Sortable for broker tabs
      this.sortable = new Sortable(container, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',

        // Only allow dragging of broker tabs, not buttons or close buttons
        filter: 'button, .add-broker-btn',
        preventOnFilter: true,

        // Prevent dragging when clicking on close buttons
        onStart: (evt) => {
          // Check if the click target is a close button or its child
          const target = evt.originalEvent.target;
          const closeButton = target.closest('span[phx-click="close_broker_tab"]');
          if (closeButton) {
            // Cancel the drag operation
            return false;
          }
        },

        // Event triggered when sorting is stopped
        onEnd: (evt) => {
          const oldIndex = evt.oldIndex;
          const newIndex = evt.newIndex;

          // Only push the event if the order actually changed
          if (oldIndex !== newIndex) {
            // Send the reordering event to the server
            this.pushEvent('reorder_broker_tabs', {
              old_index: oldIndex,
              new_index: newIndex
            });
          }
        }
      });

      // Handle close button clicks manually to ensure they work
      this.handleCloseButtonClick = (event) => {
        const closeButton = event.target.closest('span[phx-click="close_broker_tab"]');
        if (closeButton) {
          event.stopPropagation();
          event.preventDefault();

          // Get the broker name from the phx-value-name attribute
          const brokerName = closeButton.getAttribute('phx-value-name');
          if (brokerName) {
            // Send the close event to Phoenix LiveView
            this.pushEvent('close_broker_tab', { name: brokerName });
          }
        }
      };

      // Add click event listener to the container for close buttons
      container.addEventListener('click', this.handleCloseButtonClick);
    },

    destroyed() {
      // Clean up event listeners
      if (this.handleCloseButtonClick) {
        this.el.removeEventListener('click', this.handleCloseButtonClick);
      }

      // Clean up Sortable instance when the element is removed
      if (this.sortable) {
        this.sortable.destroy();
      }
    }
  },

  // Hook for handling protocol changes in connection set forms
  ProtocolSelector: {
    mounted() {
      this.handleProtocolChange = () => {
        const protocol = this.el.value;
        const sslContainer = document.getElementById('ssl-section-container');
        const portInput = document.querySelector('input[name="connection_set[port]"]');

        // Show SSL section for secure protocols
        if (sslContainer) {
          if (['mqtts', 'wss', 'quic'].includes(protocol)) {
            sslContainer.style.display = 'block';
          } else {
            sslContainer.style.display = 'none';
          }
        }

        // Update port field based on protocol
        if (portInput) {
          // Only update port if it's a default port value
          const defaultPorts = {
            mqtt: "1883",
            mqtts: "8883",
            ws: "8083",
            wss: "8084",
            quic: "14567"
          };

          // Check if current port is one of the default ports
          const isDefaultPort = Object.values(defaultPorts).includes(portInput.value);

          // Only update if it's a default port to avoid overwriting custom ports
          if (isDefaultPort) {
            portInput.value = defaultPorts[protocol] || "1883";
          }
        }
      };

      // Set initial state
      this.handleProtocolChange();

      // Add event listener for changes
      this.el.addEventListener('change', this.handleProtocolChange);
    },

    destroyed() {
      // Clean up event listener
      if (this.handleProtocolChange) {
        this.el.removeEventListener('change', this.handleProtocolChange);
      }
    }
  },

  // We're now using the onchange attribute directly on the radio buttons
  // instead of a hook for certificate type selection

  // We've removed the ConnectionStatusSortable and ConnectionStatusContainer hooks
  // as we've replaced the component with a table view

  // Hook for handling connection table animations with GSAP
  ConnectionTableAutoAnimate: {
    mounted() {
      this.setupGSAPAnimations();
      this.observeTableChanges();
    },

    updated() {
      // Re-observe table changes when component updates
      this.observeTableChanges();
    },

    setupGSAPAnimations() {
      // Set up GSAP defaults for smooth animations
      gsap.defaults({
        duration: 0.4,
        ease: "power2.out"
      });

      // Store reference to table body for animations
      this.tableBody = this.el.querySelector('tbody');
      this.previousRows = new Map();

      if (this.tableBody) {
        // Store initial state of rows
        this.captureRowState();
      }
    },

    observeTableChanges() {
      if (!this.tableBody) return;

      // Create a mutation observer to detect row changes
      if (this.observer) {
        this.observer.disconnect();
      }

      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            this.handleRowChanges(mutation);
          }
        });
      });

      this.observer.observe(this.tableBody, {
        childList: true,
        subtree: true
      });

      // Also observe status changes within existing rows
      if (this.statusObserver) {
        this.statusObserver.disconnect();
      }

      this.statusObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            this.handleStatusChange(mutation.target);
          }
        });
      });

      // Observe all status elements for changes
      const statusElements = this.tableBody.querySelectorAll('.swap input[type="checkbox"]');
      statusElements.forEach(element => {
        this.statusObserver.observe(element, {
          attributes: true,
          attributeFilter: ['checked']
        });
      });
    },

    captureRowState() {
      if (!this.tableBody) return;

      const rows = this.tableBody.querySelectorAll('tr');
      this.previousRows.clear();

      rows.forEach((row, index) => {
        const clientId = this.getRowClientId(row);
        if (clientId) {
          this.previousRows.set(clientId, {
            element: row,
            index: index,
            rect: row.getBoundingClientRect()
          });
        }
      });
    },

    getRowClientId(row) {
      // Extract client ID from the row's data or content
      const clientIdCell = row.querySelector('td:nth-child(2) span');
      return clientIdCell ? clientIdCell.textContent.trim() : null;
    },

    handleRowChanges(mutation) {
      // Handle removed nodes (connections disappearing)
      mutation.removedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowRemoval(node);
        }
      });

      // Handle added nodes (connections appearing)
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowAddition(node);
        }
      });

      // Update row state after changes
      setTimeout(() => this.captureRowState(), 50);
    },

    handleStatusChange(element) {
      // Find the row containing this status element
      const row = element.closest('tr');
      if (!row) return;

      // Add a visual indicator that the status is changing
      row.classList.add('status-changing');

      // Add a subtle pulse animation to indicate the change
      gsap.fromTo(row,
        {
          backgroundColor: 'rgba(59, 130, 246, 0.15)',
          scale: 1
        },
        {
          duration: 0.6,
          backgroundColor: 'transparent',
          scale: 1.01,
          ease: "power2.out",
          yoyo: true,
          repeat: 1,
          onComplete: () => {
            row.classList.remove('status-changing');
          }
        }
      );

      // The row removal will be handled by the main mutation observer
      // when LiveView updates the DOM
    },

    animateRowRemoval(row) {
      // Create a clone of the row for animation
      const clone = row.cloneNode(true);
      clone.style.position = 'absolute';
      clone.style.left = '0';
      clone.style.right = '0';
      clone.style.zIndex = '10';
      clone.style.pointerEvents = 'none';

      // Insert clone at the same position
      if (row.parentNode) {
        row.parentNode.insertBefore(clone, row.nextSibling);
      }

      // Animate the clone out
      gsap.to(clone, {
        duration: 0.5,
        x: -50,
        opacity: 0,
        scale: 0.95,
        ease: "power2.in",
        onComplete: () => {
          if (clone.parentNode) {
            clone.parentNode.removeChild(clone);
          }
        }
      });

      // Animate remaining rows moving up
      this.animateRemainingRows();
    },

    animateRowAddition(row) {
      // Set initial state for new row
      gsap.set(row, {
        x: 30,
        opacity: 0,
        scale: 0.98
      });

      // Animate row in
      gsap.to(row, {
        duration: 0.4,
        x: 0,
        opacity: 1,
        scale: 1,
        ease: "back.out(1.2)",
        delay: 0.1
      });
    },

    animateRemainingRows() {
      if (!this.tableBody) return;

      const currentRows = this.tableBody.querySelectorAll('tr');

      currentRows.forEach((row, index) => {
        // Add subtle animation to show the table is updating
        gsap.fromTo(row,
          {
            y: -5,
            opacity: 0.8
          },
          {
            duration: 0.3,
            y: 0,
            opacity: 1,
            ease: "power2.out",
            delay: index * 0.02 // Stagger effect
          }
        );
      });
    },

    destroyed() {
      // Clean up observers
      if (this.observer) {
        this.observer.disconnect();
      }

      if (this.statusObserver) {
        this.statusObserver.disconnect();
      }

      // Kill any running GSAP animations
      if (this.tableBody) {
        gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
      }
    }
  },



  // Hook for handling trace table smart scrolling with GSAP animations
  TraceTableSmartScroll: {
    mounted() {
      this.setupSmartScroll();
      this.setupGSAPAnimations();

      // Initial scroll to bottom when component is first mounted
      setTimeout(() => {
        this.scrollToBottom();
        this.isInitialLoad = false; // Mark initial load as complete
      }, 100);
    },

    updated() {
      // Use setTimeout to ensure DOM has been updated
      setTimeout(() => {
        this.checkScrollPosition();
        this.detectNewMessages();

        // If user was at bottom and new rows were added, scroll to bottom
        if (this.shouldScrollToBottom) {
          this.scrollToBottom();
          this.shouldScrollToBottom = false;
        }
      }, 10);
    },

    setupSmartScroll() {
      const tableBody = this.el;
      if (!tableBody) return;

      // Find the scrollable container (usually the parent with overflow)
      this.scrollContainer = this.findScrollContainer(tableBody);
      this.tableBody = tableBody;
      this.isUserAtBottom = true; // Start assuming user is at bottom
      this.scrollThreshold = 20; // Tighter threshold for more accurate detection
      this.isInitialLoad = true; // Track if this is the initial load
      this.maxDisplayMessages = 15; // Maximum messages to display
      this.isViewingLatestMessages = true; // Track if user is viewing latest messages

      // Track scroll position with debouncing
      this.scrollDebounceTimer = null;
      this.handleScroll = () => {
        if (this.scrollDebounceTimer) {
          clearTimeout(this.scrollDebounceTimer);
        }
        this.scrollDebounceTimer = setTimeout(() => {
          this.checkScrollPosition();
          this.updateViewingLatestStatus();
        }, 50);
      };

      if (this.scrollContainer) {
        this.scrollContainer.addEventListener('scroll', this.handleScroll, { passive: true });
      }

      // Store initial row count and message IDs
      this.previousRowCount = tableBody.children.length;
      this.previousMessageIds = new Set();
      this.shouldScrollToBottom = false;

      // Capture initial message IDs
      this.captureMessageIds();
    },

    setupGSAPAnimations() {
      const tableBody = this.el;
      if (!tableBody) return;

      // Set up GSAP defaults for trace table animations
      gsap.defaults({
        duration: 0.3,
        ease: "power2.out"
      });

      // Store reference to table body for animations
      this.tableBody = tableBody;
      this.previousRows = new Map();
      this.animationQueue = [];

      // Capture initial state of rows
      this.captureRowState();

      // Set up mutation observer to detect DOM changes
      this.setupMutationObserver();
    },

    setupMutationObserver() {
      if (!this.tableBody) return;

      this.observer = new MutationObserver((mutations) => {
        let hasAdditions = false;
        let hasRemovals = false;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            if (mutation.addedNodes.length > 0) {
              hasAdditions = true;
            }
            if (mutation.removedNodes.length > 0) {
              hasRemovals = true;
            }
          }
        });

        if (hasAdditions || hasRemovals) {
          // Use requestAnimationFrame to ensure DOM is settled
          requestAnimationFrame(() => {
            this.handleTableChanges();
          });
        }
      });

      this.observer.observe(this.tableBody, {
        childList: true,
        subtree: false
      });
    },

    captureRowState() {
      if (!this.tableBody) return;

      const rows = this.tableBody.querySelectorAll('tr');
      this.previousRows.clear();

      rows.forEach((row) => {
        if (row.id && row.id !== 'loading-more-row') {
          const rect = row.getBoundingClientRect();
          this.previousRows.set(row.id, {
            element: row,
            top: rect.top,
            height: rect.height
          });
        }
      });
    },

    handleTableChanges() {
      if (!this.tableBody) return;

      const currentRows = this.tableBody.querySelectorAll('tr');
      const currentRowIds = new Set();
      const newRows = [];

      // Identify current rows and find new ones
      currentRows.forEach((row) => {
        if (row.id && row.id !== 'loading-more-row') {
          currentRowIds.add(row.id);
          if (!this.previousRows.has(row.id)) {
            newRows.push(row);
          }
        }
      });

      // Animate new rows based on smart scroll logic
      if (newRows.length > 0) {
        if (this.isViewingLatestMessages && this.isUserAtBottom) {
          // User is viewing latest messages and at bottom - full animation
          this.animateNewRowsSlideIn(newRows);
        } else if (this.isUserAtBottom) {
          // User at bottom but not viewing latest - subtle animation
          this.animateNewRowsSubtle(newRows);
        } else {
          // User not at bottom - minimal animation
          this.animateNewRowsMinimal(newRows);
        }
      }

      // Update the captured state
      this.captureRowState();
    },

    captureMessageIds() {
      if (!this.tableBody) return;

      this.previousMessageIds.clear();
      const rows = this.tableBody.querySelectorAll('tr');
      rows.forEach((row) => {
        if (row.id && row.id !== 'loading-more-row') {
          this.previousMessageIds.add(row.id);
        }
      });
    },

    detectNewMessages() {
      if (!this.tableBody) return;

      const currentRows = this.tableBody.querySelectorAll('tr');
      const currentMessageIds = new Set();
      const newMessageIds = [];

      // Collect current message IDs
      currentRows.forEach((row) => {
        if (row.id && row.id !== 'loading-more-row') {
          currentMessageIds.add(row.id);
          if (!this.previousMessageIds.has(row.id)) {
            newMessageIds.push(row.id);
          }
        }
      });

      // If we have new messages and user is viewing latest, trigger auto-scroll
      if (newMessageIds.length > 0 && this.isViewingLatestMessages && this.isUserAtBottom) {
        this.shouldScrollToBottom = true;
      }

      // Update the stored message IDs
      this.previousMessageIds = currentMessageIds;
    },

    updateViewingLatestStatus() {
      if (!this.tableBody || !this.scrollContainer) return;

      const rows = this.tableBody.querySelectorAll('tr');
      const visibleRows = Array.from(rows).filter(row => row.id && row.id !== 'loading-more-row');

      if (visibleRows.length === 0) {
        this.isViewingLatestMessages = true;
        return;
      }

      // Check if we're viewing the latest messages by examining scroll position
      // and the number of visible rows vs total available
      let scrollTop, scrollHeight, clientHeight;

      if (this.scrollContainer === window) {
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        scrollTop = this.scrollContainer.scrollTop;
        scrollHeight = this.scrollContainer.scrollHeight;
        clientHeight = this.scrollContainer.clientHeight;
      }

      // Calculate if we're in the bottom portion showing latest messages
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
      const isNearBottom = scrollPercentage > 0.85; // Within 85% of bottom

      // Also check if we have exactly the max display messages or fewer
      const hasMaxOrFewerMessages = visibleRows.length <= this.maxDisplayMessages;

      this.isViewingLatestMessages = isNearBottom && (hasMaxOrFewerMessages || this.isUserAtBottom);
    },

    // GitHub Actions-style slide in animation for new messages when viewing latest
    animateNewRowsSlideIn(newRows) {
      newRows.forEach((row, index) => {
        // Set initial state - slide in from bottom with green highlight
        gsap.set(row, {
          y: 30,
          opacity: 0,
          backgroundColor: 'rgba(34, 197, 94, 0.2)',
          boxShadow: '0 4px 12px rgba(34, 197, 94, 0.4)',
          borderLeft: '4px solid rgba(34, 197, 94, 0.8)',
          scale: 0.98
        });

        // Animate row sliding in from bottom with bounce
        gsap.to(row, {
          duration: 0.6,
          y: 0,
          opacity: 1,
          scale: 1,
          ease: "back.out(1.4)",
          delay: index * 0.08 // Stagger for multiple new rows
        });

        // Fade out the green highlight after the slide animation
        gsap.to(row, {
          duration: 1.2,
          backgroundColor: 'transparent',
          boxShadow: 'none',
          borderLeft: 'none',
          ease: "power2.out",
          delay: 0.4 + (index * 0.08)
        });
      });
    },

    // Subtle animation when user is at bottom but not viewing latest messages
    animateNewRowsSubtle(newRows) {
      newRows.forEach((row, index) => {
        gsap.set(row, {
          y: 15,
          opacity: 0,
          backgroundColor: 'rgba(34, 197, 94, 0.1)'
        });

        gsap.to(row, {
          duration: 0.3,
          y: 0,
          opacity: 1,
          backgroundColor: 'transparent',
          ease: "power2.out",
          delay: index * 0.03
        });
      });
    },

    // Minimal animation when user is not at bottom
    animateNewRowsMinimal(newRows) {
      newRows.forEach((row) => {
        gsap.fromTo(row,
          { opacity: 0 },
          { opacity: 1, duration: 0.2, ease: "power1.out" }
        );
      });
    },

    // Legacy function for backward compatibility
    animateNewRows(newRows) {
      // Default to slide in animation
      this.animateNewRowsSlideIn(newRows);
    },

    findScrollContainer(element) {
      let parent = element.parentElement;
      while (parent && parent !== document.body) {
        const style = window.getComputedStyle(parent);
        // Check for the specific trace table wrapper class first
        if (parent.classList.contains('trace-message-table-wrapper')) {
          return parent;
        }
        // Then check for overflow styles
        if (style.overflow === 'auto' || style.overflow === 'scroll' ||
            style.overflowY === 'auto' || style.overflowY === 'scroll') {
          return parent;
        }
        parent = parent.parentElement;
      }
      return window; // Fallback to window
    },

    checkScrollPosition() {
      if (!this.scrollContainer || !this.tableBody) return;

      let scrollTop, scrollHeight, clientHeight;

      if (this.scrollContainer === window) {
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        scrollTop = this.scrollContainer.scrollTop;
        scrollHeight = this.scrollContainer.scrollHeight;
        clientHeight = this.scrollContainer.clientHeight;
      }

      // Check if user is near the bottom with tighter threshold
      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
      const wasAtBottom = this.isUserAtBottom;
      this.isUserAtBottom = distanceFromBottom <= this.scrollThreshold;

      // Enhanced logic for determining when to auto-scroll
      const currentRowCount = this.tableBody.children.length;

      // Only trigger auto-scroll if:
      // 1. New rows were added
      // 2. User was at bottom before the addition
      // 3. User is viewing the latest messages
      if (currentRowCount > this.previousRowCount && wasAtBottom && this.isViewingLatestMessages) {
        this.shouldScrollToBottom = true;
      }

      this.previousRowCount = currentRowCount;

      // Log scroll state for debugging (can be removed in production)
      if (window.debugTraceScroll) {
        console.log('Scroll State:', {
          isUserAtBottom: this.isUserAtBottom,
          isViewingLatestMessages: this.isViewingLatestMessages,
          distanceFromBottom,
          shouldScrollToBottom: this.shouldScrollToBottom
        });
      }
    },

    scrollToBottom() {
      if (!this.scrollContainer) return;

      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        if (this.scrollContainer === window) {
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth'
          });
        } else {
          this.scrollContainer.scrollTo({
            top: this.scrollContainer.scrollHeight,
            behavior: 'smooth'
          });
        }
      });
    },

    destroyed() {
      // Clean up scroll event listener
      if (this.scrollContainer && this.handleScroll) {
        this.scrollContainer.removeEventListener('scroll', this.handleScroll);
      }

      // Clean up scroll debounce timer
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
      }

      // Clean up mutation observer
      if (this.observer) {
        this.observer.disconnect();
      }

      // Kill any running GSAP animations
      if (this.tableBody) {
        gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
      }

      // Clear references
      this.previousMessageIds = null;
      this.previousRows = null;
    }
  },

  // Hook for handling trace row highlighting
  TraceRowHighlight: {
    mounted() {
      this.setupRowHighlighting();
    },

    updated() {
      this.setupRowHighlighting();
    },

    setupRowHighlighting() {
      const container = this.el;
      const tableBody = container.querySelector('#trace-message-table-body');
      if (!tableBody) return;

      // Remove existing event listeners to avoid duplicates
      if (this.handleRowClick) {
        tableBody.removeEventListener('click', this.handleRowClick);
      }

      // Add click event listener to the table body
      this.handleRowClick = (event) => {
        const clickedRow = event.target.closest('tr');
        if (!clickedRow || clickedRow.id === 'loading-more-row') return;

        // Remove highlight from all rows
        const allRows = tableBody.querySelectorAll('tr.trace-message-row');
        allRows.forEach(row => {
          row.classList.remove('js-highlighted');
        });

        // Add highlight to clicked row with animation
        clickedRow.classList.add('js-highlighted');

        // Add a subtle pulse effect
        clickedRow.style.animation = 'none';
        // Force reflow
        clickedRow.offsetHeight;
        clickedRow.style.animation = 'row-highlight-pulse 0.4s ease-out';

        // Remove the animation after it completes
        setTimeout(() => {
          if (clickedRow.style) {
            clickedRow.style.animation = '';
          }
        }, 400);
      };

      tableBody.addEventListener('click', this.handleRowClick);
      // Store reference for cleanup
      this.tableBody = tableBody;
    },

    destroyed() {
      if (this.tableBody && this.handleRowClick) {
        this.tableBody.removeEventListener('click', this.handleRowClick);
      }
    }
  }
};

const liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken},
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// Add custom event handlers
window.addEventListener("phx:focus_element", (e) => {
  const element = document.getElementById(e.detail.id);
  if (element) {
    setTimeout(() => {
      element.focus();
    }, 50);
  }
});

// Update certificate files visibility when modal is shown
window.addEventListener("phx:show", (_e) => {
  // Wait a bit for the DOM to be updated
  setTimeout(() => {
    window.updateCertificateFilesVisibility();
  }, 100);
});



// Initialize functionality
document.addEventListener("DOMContentLoaded", () => {
  // Initialize certificate files visibility
  window.updateCertificateFilesVisibility();
});



// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// Debug functions for smart scrolling (can be removed in production)
window.debugTraceScroll = false;
window.enableTraceScrollDebug = function() {
  window.debugTraceScroll = true;
  console.log('Trace scroll debugging enabled. Check console for scroll state logs.');
};
window.disableTraceScrollDebug = function() {
  window.debugTraceScroll = false;
  console.log('Trace scroll debugging disabled.');
};

// Function to update certificate files visibility based on selected certificate type
window.updateCertificateFilesVisibility = function() {
  const certificateFilesSection = document.getElementById('certificate-files-section');
  if (!certificateFilesSection) return;

  const selfSignedRadio = document.getElementById('certificate-type-self-signed');
  if (selfSignedRadio && selfSignedRadio.checked) {
    certificateFilesSection.style.display = 'block';
  } else {
    certificateFilesSection.style.display = 'none';
  }
}

// The lines below enable quality of life phoenix_live_reload
// development features:
//
//     1. stream server logs to the browser console
//     2. click on elements to jump to their definitions in your code editor
//
if (process.env.NODE_ENV === "development") {
  window.addEventListener("phx:live_reload:attached", ({detail: reloader}) => {
    // Enable server log streaming to client.
    // Disable with reloader.disableServerLogs()
    reloader.enableServerLogs()

    // Open configured PLUG_EDITOR at file:line of the clicked element's HEEx component
    //
    //   * click with "c" key pressed to open at caller location
    //   * click with "d" key pressed to open at function component definition location
    let keyDown
    window.addEventListener("keydown", e => keyDown = e.key)
    window.addEventListener("keyup", _e => keyDown = null)
    window.addEventListener("click", e => {
      if(keyDown === "c"){
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtCaller(e.target)
      } else if(keyDown === "d"){
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtDef(e.target)
      }
    }, true)

    window.liveReloader = reloader
  })
}

